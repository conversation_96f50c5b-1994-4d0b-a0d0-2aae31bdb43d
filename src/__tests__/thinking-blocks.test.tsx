import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { Provider } from 'jotai'
import { ReasoningContentUI } from '../renderer/components/message-parts/ToolCallPartUI'
import { autoCollapseThinkingBlocksAtom } from '../renderer/stores/atoms'
import type { Message } from '../shared/types'

// Mock the translation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}))

// Mock the jotai atom
const mockStore = new Map()

const TestProvider: React.FC<{ children: React.ReactNode; autoCollapse?: boolean }> = ({ 
  children, 
  autoCollapse = false 
}) => {
  mockStore.set(autoCollapseThinkingBlocksAtom, autoCollapse)
  return <Provider>{children}</Provider>
}

describe('ReasoningContentUI', () => {
  const mockMessage: Message = {
    id: 'test-message',
    role: 'assistant',
    content: 'Test content',
    generating: false,
    reasoningContent: 'This is a test reasoning content',
    contentParts: [],
  }

  const mockOnCopyReasoningContent = jest.fn(() => jest.fn())

  beforeEach(() => {
    jest.clearAllMocks()
    mockStore.clear()
  })

  it('should expand thinking blocks by default when autoCollapseThinkingBlocks is false', () => {
    render(
      <TestProvider autoCollapse={false}>
        <ReasoningContentUI 
          message={mockMessage} 
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // The reasoning content should be visible (expanded)
    expect(screen.getByText('This is a test reasoning content')).toBeInTheDocument()
    expect(screen.getByText('Deeply thought')).toBeInTheDocument()
  })

  it('should collapse thinking blocks by default when autoCollapseThinkingBlocks is true', () => {
    render(
      <TestProvider autoCollapse={true}>
        <ReasoningContentUI 
          message={mockMessage} 
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // The reasoning content should not be visible (collapsed)
    expect(screen.queryByText('This is a test reasoning content')).not.toBeInTheDocument()
    expect(screen.getByText('Deeply thought')).toBeInTheDocument()
  })

  it('should allow manual toggle of thinking blocks regardless of setting', () => {
    render(
      <TestProvider autoCollapse={true}>
        <ReasoningContentUI 
          message={mockMessage} 
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // Initially collapsed
    expect(screen.queryByText('This is a test reasoning content')).not.toBeInTheDocument()

    // Click to expand
    const header = screen.getByText('Deeply thought').closest('div')
    fireEvent.click(header!)

    // Should now be expanded
    expect(screen.getByText('This is a test reasoning content')).toBeInTheDocument()
  })

  it('should always expand when currently thinking (generating)', () => {
    const thinkingMessage: Message = {
      ...mockMessage,
      generating: true,
      contentParts: [{ type: 'reasoning', text: 'Currently thinking...' }],
    }

    render(
      <TestProvider autoCollapse={true}>
        <ReasoningContentUI
          message={thinkingMessage}
          part={thinkingMessage.contentParts![0] as any}
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // Should be expanded even with autoCollapse=true because it's currently thinking
    expect(screen.getByText('Currently thinking...')).toBeInTheDocument()
    expect(screen.getByText('Thinking')).toBeInTheDocument()
  })

  it('should respond to setting changes for blocks that user has not manually toggled', () => {
    const { rerender } = render(
      <TestProvider autoCollapse={false}>
        <ReasoningContentUI
          message={mockMessage}
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // Initially expanded (autoCollapse=false)
    expect(screen.getByText('This is a test reasoning content')).toBeInTheDocument()

    // Change setting to autoCollapse=true
    rerender(
      <TestProvider autoCollapse={true}>
        <ReasoningContentUI
          message={mockMessage}
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // Should now be collapsed due to setting change
    expect(screen.queryByText('This is a test reasoning content')).not.toBeInTheDocument()
  })

  it('should not respond to setting changes for blocks that user has manually toggled', () => {
    const { rerender } = render(
      <TestProvider autoCollapse={false}>
        <ReasoningContentUI
          message={mockMessage}
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // Initially expanded
    expect(screen.getByText('This is a test reasoning content')).toBeInTheDocument()

    // User manually collapses
    const header = screen.getByText('Deeply thought').closest('div')
    fireEvent.click(header!)
    expect(screen.queryByText('This is a test reasoning content')).not.toBeInTheDocument()

    // Change setting to autoCollapse=false (which would normally expand)
    rerender(
      <TestProvider autoCollapse={false}>
        <ReasoningContentUI
          message={mockMessage}
          onCopyReasoningContent={mockOnCopyReasoningContent}
        />
      </TestProvider>
    )

    // Should remain collapsed because user manually toggled it
    expect(screen.queryByText('This is a test reasoning content')).not.toBeInTheDocument()
  })
})
