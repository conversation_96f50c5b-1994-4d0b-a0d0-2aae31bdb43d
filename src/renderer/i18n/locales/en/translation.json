{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend", "{{count}} file(s) not supported: {{files}}. Supported formats: {{formats}}": "{{count}} file(s) not supported: {{files}}. Supported formats: {{formats}}", "{{count}} MCP servers imported": "{{count}} MCP servers imported", "A provider with this ID already exists. Continuing will overwrite the existing configuration.": "A provider with this ID already exists. Continuing will overwrite the existing configuration.", "About": "About", "about-introduction": "about-introduction", "about-slogan": "about-slogan", "Action": "Action", "Activate License": "Activate License", "Add": "Add", "Add at least one model to check connection": "Add at least one model to check connection", "Add Custom Provider": "Add Custom Provider", "Add Custom Server": "Add Custom Server", "Add File": "Add File", "Add MCP Server": "Add MCP Server", "Add or Import": "Add or Import", "Add provider": "Add provider", "Add Server": "Add Server", "Add your first MCP server": "Add your first MCP server", "Advanced Mode": "Advanced Mode", "ai provider no implemented paint tips": "ai provider no implemented paint tips", "AIHubMix integration in Chatbox offers 10% discount": "AIHubMix integration in Chatbox offers 10% discount", "All data is stored locally, ensuring privacy and rapid access": "All data is stored locally, ensuring privacy and rapid access", "All major AI models in one subscription": "All major AI models in one subscription", "already existed": "already existed", "An easy-to-use AI client app": "An easy-to-use AI client app", "An error occurred while sending the message.": "An error occurred while sending the message.", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.", "API Host": "API Host", "API Key": "API Key", "API KEY & License": "API KEY & License", "API key invalid!": "API key invalid!", "API Key is required to check connection": "API Key is required to check connection", "API Mode": "API Mode", "API Path": "API Path", "Are you sure you want to delete the knowledge base": "Are you sure you want to delete the knowledge base", "Are you sure you want to delete this server?": "Are you sure you want to delete this server?", "Arguments": "Arguments", "Attach Image": "Attach Image", "Attach Link": "Attach Link", "Auto": "Auto", "Auto (Use Chat Model)": "Auto (Use Chat Model)", "Auto (Use Chatbox AI)": "Auto (Use Chatbox AI)", "Auto (Use Last Used)": "Auto (Use Last Used)", "Auto-collapse code blocks": "Auto-collapse code blocks", "Auto-Generate Chat Titles": "Auto-Generate Chat Titles", "Auto-preview artifacts": "Auto-preview artifacts", "Automatic updates": "Automatic updates", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)", "Azure API Version": "Azure API Version", "Azure Endpoint": "Azure Endpoint", "Back to Previous": "Back to Previous", "Beta updates": "Beta updates", "Builtin MCP Servers": "Builtin MCP Servers", "cancel": "cancel", "Cancel": "Cancel", "cannot be empty": "cannot be empty", "Capabilities": "Capabilities", "Changelog": "Changelog", "characters": "characters", "chat": "chat", "Chat": "Cha<PERSON>", "Chat History": "Chat History", "Chat Settings": "<PERSON><PERSON>", "Chatbox AI Cloud": "Chatbox AI Cloud", "Chatbox AI Image Quota": "Chatbox AI Image Quota", "Chatbox AI License": "Chatbox AI License", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI offers a user-friendly AI solution to help you enhance productivity", "Chatbox AI provides all the essential model support required for knowledge base processing": "Chatbox AI provides all the essential model support required for knowledge base processing", "Chatbox AI Quota": "Chatbox AI Quota", "Chatbox AI Standard Model Quota": "Chatbox AI Standard Model Quota", "Chatbox Featured": "Chatbox Featured", "Chatbox OCRs images with this model and sends the text to models without image support.": "Chatbox OCRs images with this model and sends the text to models without image support.", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.", "Chatbox will automatically use this model to construct search term.": "Chatbox will automatically use this model to construct search term.", "Chatbox will automatically use this model to rename threads.": "Chatbox will automatically use this model to rename threads.", "Chatbox will use this model as the default for new chats.": "Chatbox will use this model as the default for new chats.", "Check": "Check", "Check Update": "Check Update", "Child-inappropriate content": "Child-inappropriate content", "Choose a file": "Choose a file", "Choose a knowledge base": "Choose a knowledge base", "Chunk": "Chunk", "chunks": "chunks", "clean": "clean", "clean it up": "clean it up", "Clear All Messages": "Clear All Messages", "Clear Conversation List": "Clear Conversation List", "Click here to set up": "Click here to set up", "close": "close", "Code Search": "Code Search", "Collapse": "Collapse", "Collapse thinking blocks by default": "Collapse thinking blocks by default", "Command": "Command", "Completed": "Completed", "Configuration Parsed Successfully": "Configuration Parsed Successfully", "Configure MCP server manually": "Configure MCP server manually", "Confirm": "Confirm", "Confirm to delete this custom provider?": "Confirm to delete this custom provider?", "Confirm?": "Confirm?", "Connection failed!": "Connection failed!", "Connection successful!": "Connection successful!", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.", "Continue this thread": "Continue this thread", "Conversation Settings": "Conversation Settings", "copied to clipboard": "copied to clipboard", "Copilot Avatar URL": "Copilot Avatar URL", "Copilot Name": "Copilot Name", "Copilot Prompt": "Copilot Prompt", "Copilot Prompt Demo": "Copilot Prompt Demo", "copy": "copy", "Copy reasoning content": "Copy reasoning content", "Create": "Create", "Create a New Conversation": "Create a New Conversation", "Create a New Image-Creator Conversation": "Create a New Image-Creator Conversation", "Create File": "Create File", "Create First Knowledge Base": "Create First Knowledge Base", "Create Knowledge Base": "Create Knowledge Base", "Create New Copilot": "Create New Copilot", "Create your first knowledge base to start adding documents and enhance your AI conversations with contextual information.": "Create your first knowledge base to start adding documents and enhance your AI conversations with contextual information.", "Current conversation configured with specific model settings": "Current conversation configured with specific model settings", "Current model {{modelName}} does not support image input, using OCR to process images": "Current model {{modelName}} does not support image input, using OCR to process images", "Custom": "Custom", "Custom MCP Servers": "Custom MCP Servers", "Customize settings for the current conversation": "Customize settings for the current conversation", "Dark Mode": "Dark Mode", "Data Backup": "Data Backup", "Data Backup and Restore": "Data Backup and Restore", "Data Restore": "Data Restore", "Deactivate": "Deactivate", "Deeply thought": "Deeply thought", "Default Assistant Avatar": "Default Assistant <PERSON><PERSON>", "Default Chat Model": "<PERSON><PERSON><PERSON>", "Default Models": "Default Models", "Default Settings for New Conversation": "De<PERSON>ult Set<PERSON>s for New Conversation", "Default Thread Naming Model": "Default T<PERSON><PERSON>", "delete": "delete", "Delete": "Delete", "delete confirmation": "delete confirmation", "Delete Current Session": "Delete Current Session", "Delete File": "Delete File", "Delete Knowledge Base": "Delete Knowledge Base", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.", "Details": "Details", "Disabled": "Disabled", "Display": "Display", "Display Settings": "Display Settings", "Documents": "Documents", "Done": "Done", "Drag and drop files here, or click to browse": "Drag and drop files here, or click to browse", "Drop files here": "Drop files here", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.", "E-mail": "E-mail", "e.g., Model Name, Current Date": "e.g., Model Name, Current Date", "edit": "edit", "Edit": "Edit", "Edit Avatars": "Edit Avatars", "Edit File": "Edit File", "Edit Knowledge Base": "Edit Knowledge Base", "Edit MCP Server": "Edit MCP Server", "Edit Model": "Edit Model", "Edit Thread Name": "Edit Thread Name", "Embedding": "Embedding", "Embedding Model": "Embedding Model", "Enable optional anonymous reporting of crash and event data": "Enable optional anonymous reporting of crash and event data", "Enjoying Chatbox?": "Enjoying Chatbox?", "Environment Variables": "Environment Variables", "Error Reporting": "Error Reporting", "expand": "expand", "Expand": "Expand", "Expansion Pack Quota": "Expansion Pack Quota", "Explore (community)": "Explore (community)", "Explore (official)": "Explore (official)", "export": "export", "Export Chat": "Export <PERSON>", "Export Selected Data": "Export Selected Data", "Exporting...": "Exporting...", "Failed": "Failed", "Failed to activate license, please check your license key and network connection": "Failed to activate license, please check your license key and network connection", "Failed to create knowledge base, Error: {{error}}": "Failed to create knowledge base, Error: {{error}}", "Failed to export file: {{error}}": "Failed to export file: {{error}}", "Failed to fetch Chatbox AI models config, Error: {{error}}": "Failed to fetch Chatbox AI models config, Error: {{error}}", "Failed to fetch file chunks, Error: {{error}}": "Failed to fetch file chunks, Error: {{error}}", "Failed to fetch files, Error: {{error}}": "Failed to fetch files, Error: {{error}}", "Failed to fetch knowledge base list, Error: {{error}}": "Failed to fetch knowledge base list, Error: {{error}}", "Failed to fetch models": "Failed to fetch models", "Failed to import provider": "Failed to import provider", "Failed to load Chatbox AI models configuration": "Failed to load Chatbox AI models configuration", "Failed to open file dialog: {{error}}": "Failed to open file dialog: {{error}}", "Failed to read from clipboard": "Failed to read from clipboard", "Failed to save file: {{error}}": "Failed to save file: {{error}}", "Failed to update knowledge base, Error: {{error}}": "Failed to update knowledge base, Error: {{error}}", "Failed to upload {{filename}}: {{error}}": "Failed to upload {{filename}}: {{error}}", "FAQs": "FAQs", "Favorite": "Favorite", "Feedback": "<PERSON><PERSON><PERSON>", "Fetch": "<PERSON>tch", "File Chunks": "File Chunks", "File Chunks Preview": "File Chunks Preview", "File saved to {{uri}}": "File saved to {{uri}}", "File Search": "File Search", "File Size": "File Size", "Focus on the Input Box": "Focus on the Input Box", "Focus on the Input Box and Enter Web Browsing Mode": "Focus on the Input Box and Enter Web Browsing Mode", "Follow System": "Follow System", "Font Size": "Font Size", "Format": "Format", "Function": "Function", "General Settings": "General Settings", "Generate More Images Below": "Generate More Images Below", "Get API Key": "Get API Key", "Get Files Meta": "Get <PERSON> Meta", "Get License": "Get License", "get more": "get more", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "Harmful or offensive content", "Hassle-free setup": "Hassle-free setup", "Hate speech or harassment": "Hate speech or harassment", "Hide": "<PERSON>de", "High": "High", "Home Page": "Home Page", "Homepage": "Homepage", "Hotkeys": "Hotkeys", "How to use?": "How to use?", "ID": "ID", "Ideal for both work and educational scenarios": "Ideal for both work and educational scenarios", "Ideal for work and study": "Ideal for work and study", "Image Creator": "Image Creator", "Image Creator Intro": "Image Creator In<PERSON>", "Image Style": "Image Style", "Import and Restore": "Import and Restore", "Import Error": "Import Error", "Import failed, unsupported data format": "Import failed, unsupported data format", "Import from clipboard": "Import from clipboard", "Import from JSON in clipboard": "Import from JSON in clipboard", "Import MCP servers from JSON in your clipboard": "Import MCP servers from JSON in your clipboard", "Importing...": "Importing...", "Improve Network Compatibility": "Improve Network Compatibility", "Inject default metadata": "Inject default metadata", "Insert a New Line into the Input Box": "Insert a New Line into the Input Box", "Instruction (System Prompt)": "Instruction (System Prompt)", "Invalid deep link config format": "Invalid deep link config format", "Invalid provider configuration format": "Invalid provider configuration format", "It only takes a few seconds and helps a lot.": "It only takes a few seconds and helps a lot.", "Keep only the Top <0>{{N}}</0> Conversations in List and Permanently Delete the Rest": "Keep only the Top <0>{{N}}</0> Conversations in List and Permanently Delete the Rest", "Knowledge Base": "Knowledge Base", "Knowledge Base Debug": "Knowledge Base Debug", "Knowledge Base functionality is not available on Windows ARM64 due to library compatibility issues. This feature is supported on Windows x64, macOS, and Linux.": "Knowledge Base functionality is not available on Windows ARM64 due to library compatibility issues. This feature is supported on Windows x64, macOS, and Linux.", "Language": "Language", "Large file detected. Chunks will be loaded in batches of {{count}} to optimize performance.": "Large file detected. Chunks will be loaded in batches of {{count}} to optimize performance.", "Last Session": "Last Session", "LaTeX Rendering (Requires Markdown)": "LaTeX Rendering (Requires Markdown)", "Launch at system startup": "Launch at system startup", "License Activated": "License Activated", "License expired, please check your license key": "License expired, please check your license key", "License Expiry": "License Expiry", "License not found, please check your license key": "License not found, please check your license key", "License Plan Overview": "License Plan Overview", "Light Mode": "Light Mode", "List Files": "List Files", "Load More Chunks": "Load More Chunks", "Loading chunks...": "Loading chunks...", "Loading files...": "Loading files...", "Loading more chunks...": "Loading more chunks...", "Loading webpage...": "Loading webpage...", "Local (stdio)": "Local (stdio)", "Local Mode": "Local Mode", "Low": "Low", "Make sure you have the following command installed:": "Make sure you have the following command installed:", "Manage License and Devices": "Manage License and Devices", "Markdown Rendering": "Markdown Rendering", "Max Message Count in Context": "Max Message Count in Context", "Max Output Tokens": "<PERSON> Output Tokens", "Maybe Later": "Maybe Later", "MCP server added": "MCP server added", "MCP server for accessing arXiv papers": "MCP server for accessing arXiv papers", "MCP Settings": "MCP Settings", "Medium": "Medium", "Mermaid Diagrams & Charts Rendering": "Mermaid Diagrams & Charts Rendering", "MIME Type": "MIME Type", "Misleading information": "Misleading information", "Model": "Model", "Model ID": "Model ID", "Model Provider": "Model Provider", "Model Type": "Model Type", "Models": "Models", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.", "More Images": "More Images", "Move to Conversations": "Move to Conversations", "My Assistant": "My Assistant", "My Copilots": "My Copilots", "name": "name", "Name": "Name", "Name is required": "Name is required", "Natural": "Natural", "Navigate to the Next Conversation": "Navigate to the Next Conversation", "Navigate to the Next Option (in search dialog)": "Navigate to the Next Option (in search dialog)", "Navigate to the Previous Conversation": "Navigate to the Previous Conversation", "Navigate to the Previous Option (in search dialog)": "Navigate to the Previous Option (in search dialog)", "Navigate to the Specific Conversation": "Navigate to the Specific Conversation", "network error tips": "network error tips", "Network Proxy": "Network Proxy", "network proxy error tips": "network proxy error tips", "New": "New", "New Chat": "New Chat", "New Images": "New Images", "New knowledge base name": "New knowledge base name", "New Thread": "New Thread", "Nickname": "Nickname", "No chunks available. Try converting the file to a text format before adding it to the knowledge base.": "No chunks available. Try converting the file to a text format before adding it to the knowledge base.", "No documents yet": "No documents yet", "No eligible models available": "No eligible models available", "No files were dropped": "No files were dropped", "No Knowledge Base Yet": "No Knowledge Base Yet", "No Limit": "No Limit", "No MCP servers parsed from clipboard": "No MCP servers parsed from clipboard", "No permission to write file": "No permission to write file", "No results found": "No results found", "None": "None", "not available in browser": "not available in browser", "Not set": "Not set", "Nothing found...": "Nothing found...", "Number of Images per Reply": "Number of Images per Reply", "OCR Model": "OCR Model", "One-click MCP servers for Chatbox AI subscribers": "One-click MCP servers for Chatbox AI subscribers", "OpenAI API Compatible": "OpenAI API Compatible", "optional": "optional", "or": "or", "Other concerns": "Other concerns", "Paste long text as a file": "Paste long text as a file", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.", "Pause": "Pause", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, Code...", "Pending": "Pending", "Platform Not Supported": "Platform Not Supported", "Please describe the content you want to report (Optional)": "Please describe the content you want to report (Optional)", "Please select a model": "Please select a model", "Please test before saving": "Please test before saving", "Preview": "Preview", "Privacy Policy": "Privacy Policy", "Processing failed": "Processing failed", "Processing...": "Processing...", "Prompt": "Prompt", "Provider Already Exists": "Provider Already Exists", "Provider configuration is valid and ready to import": "Provider configuration is valid and ready to import", "Provider Details": "Provider Details", "Provider not found": "Provider not found", "Provider unavailable": "Provider unavailable", "Proxy Address": "Proxy Address", "QR Code": "QR Code", "Query Knowledge Base": "Query Knowledge Base", "Quota Reset": "<PERSON><PERSON><PERSON>", "quote": "quote", "Rate Now": "Rate Now", "Read File Chunks": "Read File Chunks", "Reading file...": "Reading file...", "Reasoning": "Reasoning", "RedNote": "RedNote", "Refresh": "Refresh", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.", "Remote (http/sse)": "Remote (http/sse)", "Reply Again": "Reply Again", "Reply Again Below": "Reply Again Below", "report": "report", "Report Content": "Report Content", "Report Content ID": "Report Content ID", "Report Type": "Report Type", "Rerank": "<PERSON><PERSON>", "Rerank Model": "<PERSON><PERSON>", "Rerank Model (optional)": "Rerank Model (optional)", "reset": "reset", "Reset": "Reset", "Reset to Default": "Reset to De<PERSON>ult", "Result": "Result", "Resume": "Resume", "Retrieve License": "Retrieve License", "Retrieves up-to-date documentation and code examples for any library.": "Retrieves up-to-date documentation and code examples for any library.", "Retry": "Retry", "save": "save", "Save": "Save", "Save & Resend": "Save & Resend", "Scope": "<PERSON><PERSON>", "Search": "Search", "Search All Conversations": "Search All Conversations", "Search in Current Conversation": "Search in Current Conversation", "Search models": "Search models", "Search Provider": "Search Provider", "Search query": "Search query", "Search Term Construction Model": "Search Term Construction Model", "Search...": "Search...", "Select and configure an AI model provider": "Select and configure an AI model provider", "Select File": "Select File", "Select Knowledge Base": "Select Knowledge Base", "Select Model": "Select Model", "Select the Current Option (in search dialog)": "Select the Current Option (in search dialog)", "Send": "Send", "Send Without Generating Response": "Send Without Generating Response", "Set the maximum number of tokens for model output. Please set it within the acceptable range of the model, otherwise errors may occur.": "Set the maximum number of tokens for model output. Please set it within the acceptable range of the model, otherwise errors may occur.", "Settings": "Settings", "Sexual content": "Sexual content", "Share File": "Share File", "Share with Chatbox": "Share with Chatbox", "Show": "Show", "Show all ({{x}})": "Show all ({{x}})", "show first token latency": "show first token latency", "Show in Thread List": "Show in Thread List", "show message timestamp": "show message timestamp", "show message token count": "show message token count", "show message token usage": "show message token usage", "show message word count": "show message word count", "show model name": "show model name", "Show/Hide the Application Window": "Show/Hide the Application Window", "Show/Hide the Search Dialog": "Show/Hide the Search Dialog", "Showing {{loaded}} of {{total}} chunks": "Showing {{loaded}} of {{total}} chunks", "Showing first {{count}} chunks": "Showing first {{count}} chunks", "SiliconFlow": "SiliconFlow", "Smartest AI-Powered Services for Rapid Access": "Smartest AI-Powered Services for Rapid Access", "Spam or advertising": "Spam or advertising", "Specific model settings": "Specific model settings", "Spell Check": "Spell Check", "star": "star", "Start a New Thread": "Start a New Thread", "Startup Page": "Startup Page", "Status": "Status", "stop generating": "stop generating", "Stream output": "Stream output", "submit": "submit", "Successfully uploaded {{count}} file(s)": "Successfully uploaded {{count}} file(s)", "Successfully uploaded {{success}} of {{total}} file(s). {{failed}} file(s) failed.": "Successfully uploaded {{success}} of {{total}} file(s). {{failed}} file(s) failed.", "Support jpg or png file smaller than 5MB": "Support jpg or png file smaller than 5MB", "Supported formats": "Supported formats", "Supports a variety of advanced AI models": "Supports a variety of advanced AI models", "Survey": "Survey", "Switch": "Switch", "Tavily API Key": "Tavily API Key", "temperature": "temperature", "Temperature": "Temperature", "Terminal": "Terminal", "Test": "Test", "Thank you for your report": "Thank you for your report", "The Image Creator plugin has been activated for the current conversation": "The Image Creator plugin has been activated for the current conversation", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.", "Theme": "Theme", "Thinking": "Thinking", "Thinking Budget": "Thinking Budget", "Thinking Budget only works for 2.0 or later models": "Thinking Budget only works for 2.0 or later models", "Thinking Budget only works for 3.7 or later models": "Thinking Budget only works for 3.7 or later models", "Thinking Effort": "Thinking Effort", "Thinking Effort only works for OpenAI o-series models": "Thinking Effort only works for OpenAI o-series models", "This action cannot be undone. All documents and their embeddings will be permanently deleted.": "This action cannot be undone. All documents and their embeddings will be permanently deleted.", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.", "Thread History": "Thread History", "Tool use": "Tool use", "Tool Use": "Tool Use", "Tools": "Tools", "Top P": "Top P", "Total Chunks": "Total Chunks", "Type": "Type", "Type a command or search": "Type a command or search", "Type your question here...": "Type your question here...", "Unknown": "Unknown", "unknown error tips": "unknown error tips", "unstar": "unstar", "Update Available": "Update Available", "Upload failed: {{error}}": "Upload failed: {{error}}", "Upload Image": "Upload Image", "Upload your first document to get started": "Upload your first document to get started", "Upon import, changes will take effect immediately and existing data will be overwritten": "Upon import, changes will take effect immediately and existing data will be overwritten", "Use My Own API Key / Local Model": "Use My Own API Key / Local Model", "Used to extract text feature vectors, add in Settings - Provider - Model List": "Used to extract text feature vectors, add in Settings - Provider - Model List", "Used to get more accurate search results": "Used to get more accurate search results", "Used to preprocess image files, requires models with vision capabilities enabled": "Used to preprocess image files, requires models with vision capabilities enabled", "User Avatar": "User Avatar", "User Terms": "User Terms", "View All Copilots": "View All Copilots", "View More Plans": "View More Plans", "Violence or dangerous content": "Violence or dangerous content", "Vision": "Vision", "Vision capability is not enabled for Model {{model}}. Please enable it or set a default OCR model in <OpenSettingButton>Settings</OpenSettingButton>": "Vision capability is not enabled for Model {{model}}. Please enable it or set a default OCR model in <OpenSettingButton>Settings</OpenSettingButton>", "Vision Model": "Vision Model", "Vision Model (optional)": "Vision Model (optional)", "Vision, Drawing, File Understanding and more": "Vision, Drawing, File Understanding and more", "Vivid": "Vivid", "Web Browsing": "Web Browsing", "Web Search": "Web Search", "WeChat": "WeChat", "What can I help you with today?": "What can I help you with today?", "You have no more Chatbox AI quota left this month.": "You have no more Chatbox AI quota left this month.", "Your rating on the App Store would help make Chatbox even better!": "Your rating on the App Store would help make Chatbox even better!"}